
  <script>
    window.addEventListener('message', function (event) {
      if (event.data.type === 'hsFormCallback' && event.data.eventName === 'onFormReady') {
      formSumitable();
      $('form.hs-form>.hs-form-field').each(function() {
        if (!$(this).parent().is('fieldset')) {
          $(this).wrap('<fieldset></fieldset>');
        }
      });
    // autoFillpopulate();
    $('fieldset').children().addClass('inputWrap inputSelect');
    $('.multi-container').parents('fieldset').find('.hs-form-field').addClass('multiWrap');

    $("select").chosen({
      disable_search: true,
    hide_results_on_select: false,
        });

    $('.chosen-container span').empty();

    var allInputs = $('input:not(:checkbox):not(:radio):not(select):not(".chosen-search-input"),textarea');
    allInputs.each(function() {
      if ($(this).val() !== "") {
        $(this).parents('.hs-form-field').addClass('focused');
      }
    });

      $('.hs-dependent-field > div:first-child select').change(function () {
      $('.hs-dependent-field .hs-form-field .chosen-container span').not('.hs-dependent-field .hs-form-field:first-child .chosen-container span').empty();
          if ($(this).parents('.hs-form-field').siblings('.hs-form-field').find('.inputs-list.multi-container').length > 0) {
      $(this).parents('fieldset').find('.hs-form-field').addClass('multiWrap');
    $(this).parents('.hs-form-field').siblings('.hs-form-field').find('.inputs-list.multi-container').each(function () {
                var select_id = $(this).parent().siblings('label').attr('id');
    var container_elem = $(this).parent();
    var select = $("<select multiple class='hs-input' name='"+select_id+"' id='" + select_id + "_one'></select>");
    var option_html = $(this).parent().siblings('label').text();
                //select.append('<option value="">'+option_html+'</option>');
    container_elem.append(select);
    $(this).find('input').each(function () {
                  var input_val = $(this).attr('id');
    var input_html = $(this).attr('value');
    select.append('<option value="'+input_val+'">'+input_html+'</option>');
                });
    select.chosen({
      disable_search: true,
    hide_results_on_select: false,
    placeholder_text_multiple: option_html
                });
    select.change(function(){
      // Completely freeze all animations and transitions
      $('body').addClass('freeze-animations');
      
      // Detach the elements from DOM during changes to prevent reflows
      var $multiContainer = select.siblings('.inputs-list.multi-container');
      var $parent = $multiContainer.parent();
      var $detached = $multiContainer.detach();
      
      // Update checkboxes without visual updates
      $detached.find('input[type=checkbox]').each(function() {
        $(this).prop('checked', false);
      });
      
      selected_val = select.val();
      if (selected_val && selected_val.length) {
        for (let i = 0; i < selected_val.length; i++) {
          $detached.find('input#' + selected_val[i]).prop('checked', true);
        }
      }
      
      // Reattach the elements
      $parent.append($detached);
      
      // Update focused state
      if (selected_val && selected_val.length) {
        select.parents('fieldset').find('.hs-form-field').addClass('focused');
      } else {
        select.parents('fieldset').find('.hs-form-field').addClass('focused');
      }
      
      // Remove the freeze class after a delay
      setTimeout(function() {
        $('body').removeClass('freeze-animations');
      }, 100);
    });
              });
          } else {
      $('.hs-dependent-field > div select').not('.hs-dependent-field > div:first-child select').each(function () {
        var sibling_exists = $(this).siblings('.chosen-container').length;
        if (sibling_exists == 0) {
          $(this).chosen({
            disable_search: true,
            hide_results_on_select: false,
          });
        }
      });
          }
        });

    $("select").change(function(){
          var $this = $(this);
          var chosen_elemnt = $this.siblings('.chosen-container').find('.chosen-single > span');
    var selected_val = chosen_elemnt.text();
    var fieldset = $this.parents('fieldset').find('.hs-form-field');

    // Immediately set the focused state to prevent label jumping
    if (selected_val == '' || selected_val == 'Please Select') {
      chosen_elemnt.parent().removeClass('filled');
      fieldset.removeClass('focused');
          }else{
      chosen_elemnt.parent().addClass('filled');
      // Immediately add focused state to maintain label position
      fieldset.addClass('focused');
          }
        });


    $('.inputs-list.multi-container').each(function () {
          var select_id = $(this).parent().siblings('label').attr('id');
    var container_elem = $(this).parent();
    var select = $("<select multiple class='hs-input' name='"+select_id+"' id='" + select_id + "_one'></select>");
    var option_html = $(this).parent().siblings('label').text();
          //select.append('<option value="">'+option_html+'</option>');
    container_elem.append(select);
    $(this).find('input').each(function () {
            var input_val = $(this).attr('id');
    var input_html = $(this).attr('value');
    select.append('<option value="'+input_val+'">'+input_html+'</option>');
          });
    select.chosen({
      disable_search: true,
    hide_results_on_select: false,
    placeholder_text_multiple: option_html
          });
    select.change(function(){
      // Completely freeze all animations and transitions
      $('body').addClass('freeze-animations');
      
      // Detach the elements from DOM during changes to prevent reflows
      var $multiContainer = select.siblings('.inputs-list.multi-container');
      var $parent = $multiContainer.parent();
      var $detached = $multiContainer.detach();
      
      // Update checkboxes without visual updates
      $detached.find('input[type=checkbox]').each(function() {
        $(this).prop('checked', false);
      });
      
      selected_val = select.val();
      if (selected_val && selected_val.length) {
        for (let i = 0; i < selected_val.length; i++) {
          $detached.find('input#' + selected_val[i]).prop('checked', true);
        }
      }
      
      // Reattach the elements
      $parent.append($detached);
      
      // Update focused state
      if (selected_val && selected_val.length) {
        select.parents('fieldset').find('.hs-form-field').addClass('focused');
      } else {
        select.parents('fieldset').find('.hs-form-field').addClass('focused');
      }
      
      // Remove the freeze class after a delay
      setTimeout(function() {
        $('body').removeClass('freeze-animations');
      }, 100);
    });
        });

    $('body').on('focus', 'input, .chosen-container, textarea', function () {
      $(this).parents('.hs-form-field').addClass('focused');
    });

    // Handle focus on chosen dropdown search input
    $('body').on('focus', '.chosen-search-input', function () {
      $(this).parents('.hs-form-field').addClass('focused');
    });

    // Handle chosen dropdown events to maintain label position
    $('body').on('chosen:showing_dropdown', 'select', function() {
      $(this).parents('.hs-form-field').addClass('focused');
    });

    $('body').on('chosen:hiding_dropdown', 'select', function() {
      var $this = $(this);
      var chosen_elemnt = $this.siblings('.chosen-container').find('.chosen-single > span');
      var selected_val = chosen_elemnt.text();
      var fieldset = $this.parents('fieldset').find('.hs-form-field');

      // Keep label elevated if there's a selection, otherwise remove focus
      if (selected_val && selected_val !== '' && selected_val !== 'Please Select') {
        fieldset.addClass('focused');
        chosen_elemnt.parent().addClass('filled');
      } else {
        fieldset.removeClass('focused');
        chosen_elemnt.parent().removeClass('filled');
      }
    });

    $('body').on('blur', 'input, textarea', function () {
          var elemtn_class = $(this).attr('class');

    if (elemtn_class == 'chosen-search-input') {
            // Don't handle blur for chosen search input - let the change event handle it
            return;
          }else{
            var inputValue = $(this).val();
    if (inputValue == "") {
      $(this).removeClass('filled');
    $(this).parents('.hs-form-field').removeClass('focused');
            } else {
      $(this).addClass('filled');
            }
          }
        });

    // Check for the emmpty fields in the form
    $('body').on('change', 'input, select', function () {
      formSumitable();
        });
      };


    function formSumitable() {
      var submitButton = $('input[type="submit"]');
    var getTandCCheckbox = $('input[type="checkbox"]')
    var isTandCSelected = getTandCCheckbox[getTandCCheckbox.length - 1].checked

    function isFeildEmpty() {
      function checkEmpty(arr) {
        return Array.from(arr).filter(function (item) {
          return item.required;
        }).map(function (item) {
          return item.value;
        }).includes("");
      }

        function checkEmptySelect(arr) {
            var isEmpty = false;
    arr.each(function() {
                var modifiedId = $(this).attr('id').replaceAll("-","_") + '_chosen';
    // var modifiedId = $(this).attr('class').replaceAll("-","_") + '_chosen';
    var modifiedElement = $('#' + modifiedId);
    var isVisible = modifiedElement.is(":visible")
    var isRequired = $(this).prop('required')
    var elementContent = modifiedElement.find('span').text().trim();
                if (isVisible && isRequired && elementContent === "Please Select" && elementContent.length > 0) {
      isEmpty = true;
    return isEmpty;
                }
            });
    return isEmpty;
        }

    var isInputEmpty = checkEmpty($('input'));
    var isSelectEmpty = checkEmptySelect($('select'));

    if (!isInputEmpty && !isSelectEmpty && isTandCSelected) {
          return true;
        }

    if (isInputEmpty || isSelectEmpty || isTandCSelected) {
          return false;
        }
      }
    var isFormFilled = isFeildEmpty();
    submitButton.attr('disabled', !isFormFilled);
    }
   
    });
  </script>
