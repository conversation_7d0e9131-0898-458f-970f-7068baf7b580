window.addEventListener('message', function (event) {
  if (event.data.type === 'hsFormCallback' && event.data.eventName === 'onFormReady') {
    formSumitable();
    $('form.hs-form>.hs-form-field').each(function() {
      if (!$(this).parent().is('fieldset')) {
        $(this).wrap('<fieldset></fieldset>');
      }
    });
  // autoFillpopulate();
  $('fieldset').children().addClass('inputWrap inputSelect');
  $('.multi-container').parents('fieldset').find('.hs-form-field').addClass('multiWrap');

  $("select").chosen({
    disable_search: true,
  hide_results_on_select: false,
      });

  $('.chosen-container span').empty();

  var allInputs = $('input:not(:checkbox):not(:radio):not(select):not(".chosen-search-input"),textarea');
  allInputs.each(function() {
    if ($(this).val() !== "") {
      $(this).parents('.hs-form-field').addClass('focused');
    }
  });

    $('.hs-dependent-field > div:first-child select').change(function () {
    $('.hs-dependent-field .hs-form-field .chosen-container span').not('.hs-dependent-field .hs-form-field:first-child .chosen-container span').empty();
        if ($(this).parents('.hs-form-field').siblings('.hs-form-field').find('.inputs-list.multi-container').length > 0) {
    $(this).parents('fieldset').find('.hs-form-field').addClass('multiWrap');
  $(this).parents('.hs-form-field').siblings('.hs-form-field').find('.inputs-list.multi-container').each(function () {
              var select_id = $(this).parent().siblings('label').attr('id');
  var container_elem = $(this).parent();
  var select = $("<select multiple class='hs-input' name='"+select_id+"' id='" + select_id + "_one'></select>");
  var option_html = $(this).parent().siblings('label').text();
              //select.append('<option value="">'+option_html+'</option>');
  container_elem.append(select);
  $(this).find('input').each(function () {
                var input_val = $(this).attr('id');
  var input_html = $(this).attr('value');
  select.append('<option value="'+input_val+'">'+input_html+'</option>');
              });
  select.chosen({
    disable_search: true,
  hide_results_on_select: false,
  placeholder_text_multiple: option_html
              });
  select.change(function(){
    select.siblings('.inputs-list.multi-container').find('input[type=checkbox]').each(function () {
      var checkbox = $(this);
      if (checkbox.is(":checked")) {
        checkbox.parent('label').click();
        checkbox.prop('checked', false);
      }
    });
  selected_val = $(this).val();
  if (selected_val != '') {
                  for (let i = 0; i < selected_val.length; i++) {
    $('input#' + selected_val[i]).prop('checked', true);
  $('label[for="'+selected_val[i]+'"]').click();
                  }
  select.parents('fieldset').find('.hs-form-field').addClass('focused');
                }
  else{
    select.parents('fieldset').find('.hs-form-field').addClass('focused');
                }
              });
            });
        } else {
    $('.hs-dependent-field > div select').not('.hs-dependent-field > div:first-child select').each(function () {
      var sibling_exists = $(this).siblings('.chosen-container').length;
      if (sibling_exists == 0) {
        $(this).chosen({
          disable_search: true,
          hide_results_on_select: false,
        });
      }
    });
        }
      });

  $("select").change(function(){
        var chosen_elemnt = $(this).siblings('.chosen-container').find('.chosen-single > span');
  var selected_val = chosen_elemnt.text();
  if (selected_val == '') {
    chosen_elemnt.parent().removeClass('filled');
  $(this).parents('fieldset').find('.hs-form-field').removeClass('focused');
        }else{
    chosen_elemnt.parent().addClass('filled');
  $(this).parents('fieldset').find('.hs-form-field').addClass('focused');
        }
      });


  $('.inputs-list.multi-container').each(function () {
        var select_id = $(this).parent().siblings('label').attr('id');
  var container_elem = $(this).parent();
  var select = $("<select multiple class='hs-input' name='"+select_id+"' id='" + select_id + "_one'></select>");
  var option_html = $(this).parent().siblings('label').text();
        //select.append('<option value="">'+option_html+'</option>');
  container_elem.append(select);
  $(this).find('input').each(function () {
          var input_val = $(this).attr('id');
  var input_html = $(this).attr('value');
  select.append('<option value="'+input_val+'">'+input_html+'</option>');
        });
  select.chosen({
    disable_search: true,
  hide_results_on_select: false,
  placeholder_text_multiple: option_html
        });
  select.change(function(){
    select.siblings('.inputs-list.multi-container').find('input[type=checkbox]').each(function () {
      var checkbox = $(this);
      if (checkbox.is(":checked")) {
        checkbox.parent('label').click();
        checkbox.prop('checked', false);
      }
    });
  selected_val = $(this).val();
  if (selected_val != '') {
            for (let i = 0; i < selected_val.length; i++) {
    $('input#' + selected_val[i]).prop('checked', true);
  $('label[for="'+selected_val[i]+'"]').click();
            }
  select.parents('fieldset').find('.hs-form-field').addClass('focused');
          }
  else{
    select.parents('fieldset').find('.hs-form-field').addClass('focused');
          }
        });
      });

  $('body').on('focus', 'input, .chosen-container, textarea', function () {
    $(this).parents('.hs-form-field').addClass('focused');
  });



  $('body').on('blur', 'input, textarea', function () {
        var elemtn_class = $(this).attr('class');
  if (elemtn_class == 'chosen-search-input') {
          if ($(this).parents('fieldset').find('.chosen-container').hasClass('chosen-container-multi')) {
            var chosen_elemnt = $(this).parents('fieldset').find('select');
  var selected_val = chosen_elemnt.val();
          }else{
            var chosen_elemnt = $(this).parents('fieldset').find('.chosen-container').find('.chosen-single > span');
  var selected_val = chosen_elemnt.text();
          }

  if (selected_val == '') {
    chosen_elemnt.parent().removeClass('filled');
  $(this).parents('fieldset').find('.hs-form-field').removeClass('focused');
          }else{
    chosen_elemnt.parent().addClass('filled');
  $(this).parents('fieldset').find('.hs-form-field').addClass('focused');
          }
        }else{
          var inputValue = $(this).val();
  if (inputValue == "") {
    $(this).removeClass('filled');
  $(this).parents('.hs-form-field').removeClass('focused');
          } else {
    $(this).addClass('filled');
          }
        }
      });

  // Check for the emmpty fields in the form
  $('body').on('change', 'input, select', function () {
    formSumitable();
      });
    };


  function formSumitable() {
    var submitButton = $('input[type="submit"]');
  var getTandCCheckbox = $('input[type="checkbox"]')
  var isTandCSelected = getTandCCheckbox[getTandCCheckbox.length - 1].checked

  function isFeildEmpty() {
    function checkEmpty(arr) {
      return Array.from(arr).filter(function (item) {
        return item.required;
      }).map(function (item) {
        return item.value;
      }).includes("");
    }

      function checkEmptySelect(arr) {
          var isEmpty = false;
  arr.each(function() {
              var modifiedId = $(this).attr('id').replaceAll("-","_") + '_chosen';
  // var modifiedId = $(this).attr('class').replaceAll("-","_") + '_chosen';
  var modifiedElement = $('#' + modifiedId);
  var isVisible = modifiedElement.is(":visible")
  var isRequired = $(this).prop('required')
  var elementContent = modifiedElement.find('span').text().trim();
              if (isVisible && isRequired && elementContent === "Please Select" && elementContent.length > 0) {
    isEmpty = true;
  return isEmpty;
              }
          });
  return isEmpty;
      }

  var isInputEmpty = checkEmpty($('input'));
  var isSelectEmpty = checkEmptySelect($('select'));

  if (!isInputEmpty && !isSelectEmpty && isTandCSelected) {
        return true;
      }

  if (isInputEmpty || isSelectEmpty || isTandCSelected) {
        return false;
      }
    }

  var isFormFilled = isFeildEmpty();
  submitButton.attr('disabled', !isFormFilled);
  }

  // function autoFillpopulate() {
    //   let fieldNames = []
    //   for (let i = 0; i < $('.chosen-single').length; i++) {
    //     $('.chosen-single').eq(i).text($('.is-selected').eq(i).text());
    //   }
    //   for (i = 0; i < $('.hs-input').length; i++) {
    //     fieldNames.push($('.hs-input')[i].name)
    //   }
    //   var allFields = fieldNames.map(name => $(`[name=${name}]`));
    //   var isSelect = allFields.map(ele => $(ele).is('input, select:not([multiple])'));
    //   fieldNames.map(name => $(`[name=${name}]`).val() && $(`[name=${name}]`).parents('.hs-form-field').addClass('focused'));
    // }

  });
